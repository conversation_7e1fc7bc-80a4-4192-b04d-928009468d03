/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/service/GenTableServiceImpl.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/controller/GenController.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/service/GenTableColumnServiceImpl.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/util/GenUtils.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/util/VelocityInitializer.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/domain/GenTable.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/service/IGenTableColumnService.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/domain/GenTableColumn.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/mapper/GenTableMapper.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/mapper/GenTableColumnMapper.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/config/GenConfig.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/service/IGenTableService.java
/Users/<USER>/projects/recruitment-platform/recruitment-api/ruoyi-generator/src/main/java/com/ruoyi/generator/util/VelocityUtils.java
